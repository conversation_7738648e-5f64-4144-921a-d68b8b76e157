#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
load("//bazel:GenTestRules.bzl", "GenTestRules")

java_library(
    name = "proxy",
    srcs = glob(["src/main/java/**/*.java"]),
    visibility = ["//visibility:public"],
    deps = [
        "//acl",
        "//broker",
        "//client",
        "//common",
        "//remoting",
        "//srvutil",
        "@maven//:ch_qos_logback_logback_classic",
        "@maven//:ch_qos_logback_logback_core",
        "@maven//:com_alibaba_fastjson",
        "@maven//:com_github_ben_manes_caffeine_caffeine",
        "@maven//:com_github_luben_zstd_jni",
        "@maven//:com_google_code_findbugs_jsr305",
        "@maven//:com_google_guava_guava",
        "@maven//:com_google_protobuf_protobuf_java",
        "@maven//:com_google_protobuf_protobuf_java_util",
        "@maven//:commons_cli_commons_cli",
        "@maven//:commons_codec_commons_codec",
        "@maven//:commons_collections_commons_collections",
        "@maven//:commons_validator_commons_validator",
        "@maven//:io_grpc_grpc_api",
        "@maven//:io_grpc_grpc_context",
        "@maven//:io_grpc_grpc_netty_shaded",
        "@maven//:io_grpc_grpc_services",
        "@maven//:io_grpc_grpc_stub",
        "@maven//:io_netty_netty_all",
        "@maven//:io_github_aliyunmq_rocketmq_grpc_netty_codec_haproxy",
        "@maven//:io_openmessaging_storage_dledger",
        "@maven//:io_opentelemetry_opentelemetry_api",
        "@maven//:io_opentelemetry_opentelemetry_exporter_otlp",
        "@maven//:io_opentelemetry_opentelemetry_exporter_prometheus",
        "@maven//:io_opentelemetry_opentelemetry_exporter_logging",
        "@maven//:io_opentelemetry_opentelemetry_exporter_logging_otlp",
        "@maven//:io_opentelemetry_opentelemetry_sdk",
        "@maven//:io_opentelemetry_opentelemetry_sdk_common",
        "@maven//:io_opentelemetry_opentelemetry_sdk_metrics",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:org_apache_rocketmq_rocketmq_proto",
        "@maven//:org_checkerframework_checker_qual",
        "@maven//:org_lz4_lz4_java",
        "@maven//:org_slf4j_slf4j_api",
        "@maven//:io_github_aliyunmq_rocketmq_slf4j_api",
        "@maven//:org_slf4j_jul_to_slf4j",
        "@maven//:io_github_aliyunmq_rocketmq_logback_classic",
    ],
)

java_library(
    name = "tests",
    srcs = glob(["src/test/java/**/*.java"]),
    resources = [
        "src/test/resources/mockito-extensions/org.mockito.plugins.MockMaker",
        "src/test/resources/rmq-proxy-home/conf/broker.conf",
        "src/test/resources/rmq-proxy-home/conf/logback_proxy.xml",
        "src/test/resources/rmq-proxy-home/conf/rmq-proxy.json",
    ],
    visibility = ["//visibility:public"],
    deps = [
	"//acl",
        ":proxy",
        "//:test_deps",
        "//broker",
        "//client",
        "//common",
        "//remoting",
        "@maven//:ch_qos_logback_logback_core",
        "@maven//:com_alibaba_fastjson",
        "@maven//:com_github_ben_manes_caffeine_caffeine",
        "@maven//:com_google_guava_guava",
        "@maven//:com_google_protobuf_protobuf_java",
        "@maven//:com_google_protobuf_protobuf_java_util",
        "@maven//:io_grpc_grpc_api",
        "@maven//:io_grpc_grpc_context",
        "@maven//:io_grpc_grpc_netty_shaded",
        "@maven//:io_grpc_grpc_stub",
        "@maven//:io_netty_netty_all",
        "@maven//:io_github_aliyunmq_rocketmq_grpc_netty_codec_haproxy",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:io_opentelemetry_opentelemetry_exporter_otlp",
        "@maven//:io_opentelemetry_opentelemetry_exporter_prometheus",
        "@maven//:io_opentelemetry_opentelemetry_sdk",
        "@maven//:org_apache_rocketmq_rocketmq_proto",
        "@maven//:org_checkerframework_checker_qual",
        "@maven//:org_slf4j_slf4j_api",
        "@maven//:org_springframework_spring_core",
    	"@maven//:org_jetbrains_annotations",
        "@maven//:org_slf4j_jul_to_slf4j",
        "@maven//:io_netty_netty_tcnative_boringssl_static",
        "@maven//:commons_codec_commons_codec",
    ],
)

GenTestRules(
    name = "GeneratedTestRules",
    exclude_tests = [
        "src/test/java/org/apache/rocketmq/proxy/config/InitConfigTest",
    ],
    test_files = glob(["src/test/java/**/*Test.java"]),
    deps = [
        ":tests",
    ],
)
