<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<assembly>
    <id>all</id>
    <includeBaseDirectory>true</includeBaseDirectory>
    <formats>
        <format>dir</format>
        <format>tar.gz</format>
        <format>zip</format>
    </formats>
    <fileSets>
        <fileSet>
            <directory>../</directory>
            <includes>
                <include>README.md</include>
            </includes>
        </fileSet>

        <fileSet>
            <includes>
                <include>conf/**</include>
                <include>benchmark/*</include>
            </includes>
        </fileSet>

        <fileSet>
            <includes>
                <include>bin/**</include>
            </includes>
            <fileMode>0755</fileMode>
        </fileSet>
    </fileSets>

    <files>
        <file>
            <source>LICENSE-BIN</source>
            <destName>LICENSE</destName>
        </file>
        <file>
            <source>NOTICE-BIN</source>
            <destName>NOTICE</destName>
        </file>
        <file>
            <source>../broker/src/main/resources/rmq.broker.logback.xml</source>
            <destName>conf/rmq.broker.logback.xml</destName>
        </file>
        <file>
            <source>../client/src/main/resources/rmq.client.logback.xml</source>
            <destName>conf/rmq.client.logback.xml</destName>
        </file>
        <file>
            <source>../controller/src/main/resources/rmq.controller.logback.xml</source>
            <destName>conf/rmq.controller.logback.xml</destName>
        </file>
        <file>
            <source>../namesrv/src/main/resources/rmq.namesrv.logback.xml</source>
            <destName>conf/rmq.namesrv.logback.xml</destName>
        </file>
        <file>
            <source>../tools/src/main/resources/rmq.tools.logback.xml</source>
            <destName>conf/rmq.tools.logback.xml</destName>
        </file>
        <file>
            <source>../proxy/src/main/resources/rmq.proxy.logback.xml</source>
            <destName>conf/rmq.proxy.logback.xml</destName>
        </file>
    </files>

    <moduleSets>
        <moduleSet>
            <useAllReactorProjects>true</useAllReactorProjects>
            <includes>
                <include>org.apache.rocketmq:rocketmq-container</include>
                <include>org.apache.rocketmq:rocketmq-broker</include>
                <include>org.apache.rocketmq:rocketmq-tools</include>
                <include>org.apache.rocketmq:rocketmq-client</include>
                <include>org.apache.rocketmq:rocketmq-namesrv</include>
                <include>org.apache.rocketmq:rocketmq-example</include>
                <include>org.apache.rocketmq:rocketmq-openmessaging</include>
                <include>org.apache.rocketmq:rocketmq-controller</include>
            </includes>
            <binaries>
                <outputDirectory>lib/</outputDirectory>
                <unpack>false</unpack>
                <dependencySets>
                    <dependencySet>
                        <outputDirectory>lib/</outputDirectory>
                        <excludes>
                            <exclude>io.jaegertracing:jaeger-core</exclude>
                            <exclude>io.jaegertracing:jaeger-client</exclude>
                        </excludes>
                    </dependencySet>
                </dependencySets>
            </binaries>
        </moduleSet>
    </moduleSets>
</assembly>
