/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.rocketmq.store.ha;

import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import org.apache.rocketmq.common.MixAll;
import org.apache.rocketmq.common.ServiceThread;
import org.apache.rocketmq.common.constant.LoggerName;
import org.apache.rocketmq.logging.org.slf4j.Logger;
import org.apache.rocketmq.logging.org.slf4j.LoggerFactory;
import org.apache.rocketmq.store.CommitLog;
import org.apache.rocketmq.store.DefaultMessageStore;
import org.apache.rocketmq.store.PutMessageSpinLock;
import org.apache.rocketmq.store.PutMessageStatus;
import org.apache.rocketmq.store.ha.autoswitch.AutoSwitchHAConnection;
import org.apache.rocketmq.store.ha.autoswitch.AutoSwitchHAService;

/**
 * GroupTransferService 负责处理主从复制中的消息传输确认服务。
 *
 * <p>该服务主要功能包括：</p>
 * <ul>
 *   <li>接收并处理来自 CommitLog 的组提交请求</li>
 *   <li>等待消息成功传输到指定数量的从节点</li>
 *   <li>支持普通 HA 模式和 AutoSwitch HA 模式</li>
 *   <li>处理超时和失败情况</li>
 * </ul>
 *
 * <p>工作原理：</p>
 * <ol>
 *   <li>通过 putRequest() 接收组提交请求</li>
 *   <li>使用双缓冲机制处理请求队列</li>
 *   <li>在 doWaitTransfer() 中等待消息传输到从节点</li>
 *   <li>根据 ACK 数量和超时时间判断传输是否成功</li>
 * </ol>
 *
 * @see HAService
 * @see CommitLog.GroupCommitRequest
 * @see AutoSwitchHAService
 */
public class GroupTransferService extends ServiceThread {

    private static final Logger log = LoggerFactory.getLogger(LoggerName.STORE_LOGGER_NAME);

    /** 用于通知传输状态变化的等待通知对象 */
    private final WaitNotifyObject notifyTransferObject = new WaitNotifyObject();

    /** 保护请求队列的自旋锁 */
    private final PutMessageSpinLock lock = new PutMessageSpinLock();

    /** 默认消息存储服务 */
    private final DefaultMessageStore defaultMessageStore;

    /** 高可用服务实例 */
    private final HAService haService;

    /** 写请求队列，用于接收新的组提交请求 */
    private volatile List<CommitLog.GroupCommitRequest> requestsWrite = new LinkedList<>();

    /** 读请求队列，用于处理待确认的组提交请求 */
    private volatile List<CommitLog.GroupCommitRequest> requestsRead = new LinkedList<>();

    /**
     * 构造函数，初始化 GroupTransferService
     *
     * @param haService 高可用服务实例
     * @param defaultMessageStore 默认消息存储服务
     */
    public GroupTransferService(final HAService haService, final DefaultMessageStore defaultMessageStore) {
        this.haService = haService;
        this.defaultMessageStore = defaultMessageStore;
    }

    /**
     * 提交一个组提交请求到写队列
     *
     * <p>该方法是线程安全的，使用自旋锁保护写队列。
     * 请求被添加到写队列后，会唤醒服务线程进行处理。</p>
     *
     * @param request 组提交请求，包含需要确认的偏移量和超时时间
     */
    public void putRequest(final CommitLog.GroupCommitRequest request) {
        lock.lock();
        try {
            this.requestsWrite.add(request);
        } finally {
            lock.unlock();
        }
        wakeup();
    }

    /**
     * 通知有新的传输完成
     *
     * <p>当 HAService 检测到有新的数据传输到从节点时，
     * 会调用此方法唤醒等待中的传输确认逻辑。</p>
     */
    public void notifyTransferSome() {
        this.notifyTransferObject.wakeup();
    }

    /**
     * 交换读写请求队列
     *
     * <p>使用双缓冲机制，将写队列和读队列进行交换。
     * 这样可以在处理当前批次请求的同时，继续接收新的请求，
     * 避免阻塞新请求的提交。</p>
     *
     * <p>交换过程：</p>
     * <ol>
     *   <li>获取锁保证线程安全</li>
     *   <li>将当前的写队列设为新的读队列</li>
     *   <li>将当前的读队列设为新的写队列</li>
     * </ol>
     */
    private void swapRequests() {
        lock.lock();
        try {
            List<CommitLog.GroupCommitRequest> tmp = this.requestsWrite;
            this.requestsWrite = this.requestsRead;
            this.requestsRead = tmp;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 等待消息传输到从节点的核心逻辑
     *
     * <p>该方法处理读队列中的所有组提交请求，等待消息成功传输到指定数量的从节点。
     * 支持两种模式：</p>
     * <ul>
     *   <li><b>普通 HA 模式</b>：等待指定数量的从节点确认</li>
     *   <li><b>AutoSwitch HA 模式</b>：等待 SyncStateSet 中的所有节点确认</li>
     * </ul>
     *
     * <p>对于每个请求，会在超时时间内循环检查传输状态，
     * 直到满足确认条件或超时为止。</p>
     */
    private void doWaitTransfer() {
        if (!this.requestsRead.isEmpty()) {
            for (CommitLog.GroupCommitRequest req : this.requestsRead) {
                boolean transferOK = false;

                // 获取请求的截止时间
                long deadLine = req.getDeadLine();
                // 判断是否需要等待 SyncStateSet 中的所有节点确认
                final boolean allAckInSyncStateSet = req.getAckNums() == MixAll.ALL_ACK_IN_SYNC_STATE_SET;

                // 在超时时间内循环等待传输完成
                for (int i = 0; !transferOK && deadLine - System.nanoTime() > 0; i++) {
                    // 第一次循环之后，等待传输通知
                    if (i > 0) {
                        this.notifyTransferObject.waitForRunning(1);
                    }

                    // 如果不需要等待 SyncStateSet 且只需要 1 个确认（即只有 master）
                    if (!allAckInSyncStateSet && req.getAckNums() <= 1) {
                        // 直接检查 master 的推送偏移量是否满足要求
                        transferOK = haService.getPush2SlaveMaxOffset().get() >= req.getNextOffset();
                        continue;
                    }

                    // AutoSwitch HA 模式：需要等待 SyncStateSet 中的所有节点确认
                    if (allAckInSyncStateSet && this.haService instanceof AutoSwitchHAService) {
                        // 在此模式下，必须等待 SyncStateSet 中的所有副本确认
                        final AutoSwitchHAService autoSwitchHAService = (AutoSwitchHAService) this.haService;
                        final Set<Long> syncStateSet = autoSwitchHAService.getSyncStateSet();

                        // 如果 SyncStateSet 只有 master 一个节点
                        if (syncStateSet.size() <= 1) {
                            // 只有 master，直接认为传输成功
                            transferOK = true;
                            break;
                        }

                        // 计算确认数量（包含 master）
                        int ackNums = 1;
                        for (HAConnection conn : haService.getConnectionList()) {
                            final AutoSwitchHAConnection autoSwitchHAConnection = (AutoSwitchHAConnection) conn;
                            // 检查连接是否在 SyncStateSet 中，且已确认偏移量满足要求
                            if (syncStateSet.contains(autoSwitchHAConnection.getSlaveId()) &&
                                autoSwitchHAConnection.getSlaveAckOffset() >= req.getNextOffset()) {
                                ackNums++;
                            }
                            // 如果确认数量达到 SyncStateSet 大小，传输成功
                            if (ackNums >= syncStateSet.size()) {
                                transferOK = true;
                                break;
                            }
                        }
                    } else {
                        // 普通 HA 模式：等待指定数量的从节点确认
                        // 计算确认数量（包含 master）
                        int ackNums = 1;
                        for (HAConnection conn : haService.getConnectionList()) {
                            // TODO: 必须确保每个 HAConnection 代表不同的从节点
                            // 解决方案：考虑为每个不同的从节点分配唯一且固定的 IP:PORT

                            // 检查从节点的确认偏移量是否满足要求
                            if (conn.getSlaveAckOffset() >= req.getNextOffset()) {
                                ackNums++;
                            }
                            // 如果确认数量达到要求，传输成功
                            if (ackNums >= req.getAckNums()) {
                                transferOK = true;
                                break;
                            }
                        }
                    }
                }

                // 如果传输未成功，记录警告日志
                if (!transferOK) {
                    log.warn("transfer message to slave timeout, offset : {}, request acks: {}",
                        req.getNextOffset(), req.getAckNums());
                }

                // 唤醒等待的客户端，返回传输结果
                req.wakeupCustomer(transferOK ? PutMessageStatus.PUT_OK : PutMessageStatus.FLUSH_SLAVE_TIMEOUT);
            }

            // 清空读队列，准备处理下一批请求
            this.requestsRead = new LinkedList<>();
        }
    }

    /**
     * 服务线程的主运行方法
     *
     * <p>该方法会持续运行直到服务停止，主要逻辑：</p>
     * <ol>
     *   <li>等待被唤醒或超时（10ms）</li>
     *   <li>处理待确认的传输请求</li>
     *   <li>捕获并记录异常</li>
     * </ol>
     */
    @Override
    public void run() {
        log.info(this.getServiceName() + " service started");

        while (!this.isStopped()) {
            try {
                // 等待 10ms 或被唤醒
                this.waitForRunning(10);
                // 处理传输确认逻辑
                this.doWaitTransfer();
            } catch (Exception e) {
                log.warn(this.getServiceName() + " service has exception. ", e);
            }
        }

        log.info(this.getServiceName() + " service end");
    }

    /**
     * 等待结束时的回调方法
     *
     * <p>在每次等待结束时交换读写队列，
     * 确保新的请求能够被及时处理。</p>
     */
    @Override
    protected void onWaitEnd() {
        this.swapRequests();
    }

    /**
     * 获取服务名称
     *
     * @return 服务名称，如果在 Broker 容器中会包含 Broker 标识符
     */
    @Override
    public String getServiceName() {
        if (defaultMessageStore != null && defaultMessageStore.getBrokerConfig().isInBrokerContainer()) {
            return defaultMessageStore.getBrokerIdentity().getIdentifier() + GroupTransferService.class.getSimpleName();
        }
        return GroupTransferService.class.getSimpleName();
    }
}
