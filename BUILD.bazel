#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
load("@bazel_toolchains//rules/exec_properties:exec_properties.bzl", "create_rbe_exec_properties_dict")

platform(
	name = "custom_platform",
    # Inherit from the platform target generated by 'rbe_configs_gen' assuming the generated configs
    # were imported as a Bazel external repository named 'rbe_default'. If you extracted the
    # generated configs elsewhere in your source repository, replace the following with the label
    # to the 'platform' target in the generated configs.
	parents = ["@rbe_default//config:platform"],
    # Example custom execution property instructing RBE to use e2-standard-2 GCE VMs.
	exec_properties = create_rbe_exec_properties_dict(
		container_image = "ubuntu:latest",
	),
)

java_library(
    name = "test_deps",
    visibility = ["//visibility:public"],
    exports = [
        "@maven//:junit_junit",
        "@maven//:org_assertj_assertj_core",
        "@maven//:org_hamcrest_hamcrest_library",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_powermock_powermock_module_junit4",
        "@maven//:org_powermock_powermock_api_mockito2",
        "@maven//:org_hamcrest_hamcrest_core",
        "@maven//:ch_qos_logback_logback_classic",
        "@maven//:org_awaitility_awaitility",
        "@maven//:org_openjdk_jmh_jmh_core",
        "@maven//:org_openjdk_jmh_jmh_generator_annprocess",
    ],
)
